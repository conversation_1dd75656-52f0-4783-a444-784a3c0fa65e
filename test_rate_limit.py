#!/usr/bin/env python3
"""
Test script to demonstrate the rate limiting functionality of the Flask server.
"""

import requests
import json
import time

BASE_URL = "http://localhost:5001"  # Updated to match the server port

def test_rate_limiting():
    """Test the rate limiting functionality."""
    print("Testing Rate Limiting (2 requests per 5 seconds)")
    print("=" * 60)
    
    # Test data
    test_data = {"test": "rate_limit_check"}
    
    # Make requests rapidly to trigger rate limiting
    for i in range(5):
        try:
            print(f"\nRequest {i+1}:")
            start_time = time.time()
            
            response = requests.post(BASE_URL, json=test_data)
            
            end_time = time.time()
            response_time = round((end_time - start_time) * 1000, 2)
            
            print(f"  Status Code: {response.status_code}")
            print(f"  Response Time: {response_time}ms")
            
            if response.status_code == 200:
                result = response.json()
                print(f"  Hash: {result.get('hash', 'N/A')[:16]}...")
            elif response.status_code == 429:
                result = response.json()
                print(f"  Error: {result.get('error', 'Rate limit exceeded')}")
                print(f"  Message: {result.get('message', 'Too many requests')}")
                print(f"  Retry After: {result.get('retry_after', 'Unknown')}")
            else:
                print(f"  Unexpected status code: {response.status_code}")
                print(f"  Response: {response.text}")
            
            # Small delay between requests to see the timing
            time.sleep(0.5)
            
        except requests.exceptions.ConnectionError:
            print(f"  Error: Could not connect to server at {BASE_URL}")
            print("  Make sure the Flask server is running")
            break
        except Exception as e:
            print(f"  Error: {e}")

def test_rate_limit_recovery():
    """Test that rate limiting recovers after the time window."""
    print("\n\nTesting Rate Limit Recovery")
    print("=" * 60)
    
    # First, trigger rate limiting
    print("1. Triggering rate limit...")
    for i in range(3):
        try:
            response = requests.get(f"{BASE_URL}?test=recovery_{i}")
            print(f"   Request {i+1}: Status {response.status_code}")
        except:
            print(f"   Request {i+1}: Failed")
    
    # Wait for the rate limit window to reset
    print("\n2. Waiting 6 seconds for rate limit to reset...")
    time.sleep(6)
    
    # Try again - should work now
    print("\n3. Testing after rate limit reset:")
    try:
        response = requests.get(f"{BASE_URL}?test=recovery_after_wait")
        print(f"   Status Code: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"   Success! Hash: {result.get('hash', 'N/A')[:16]}...")
        else:
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"   Error: {e}")

def test_health_endpoint_rate_limit():
    """Test the health endpoint rate limiting (should be more lenient)."""
    print("\n\nTesting Health Endpoint Rate Limiting (10 requests per 5 seconds)")
    print("=" * 60)
    
    success_count = 0
    rate_limited_count = 0
    
    # Make multiple requests to health endpoint
    for i in range(12):
        try:
            response = requests.get(f"{BASE_URL}/health")
            if response.status_code == 200:
                success_count += 1
                print(f"Request {i+1}: ✓ Success")
            elif response.status_code == 429:
                rate_limited_count += 1
                print(f"Request {i+1}: ✗ Rate limited")
            time.sleep(0.2)  # Small delay
        except Exception as e:
            print(f"Request {i+1}: Error - {e}")
    
    print(f"\nResults:")
    print(f"  Successful requests: {success_count}")
    print(f"  Rate limited requests: {rate_limited_count}")
    print(f"  Expected: ~10 successful, ~2 rate limited")

if __name__ == '__main__':
    print("Flask Rate Limiting Test")
    print("Make sure the Flask server is running on port 5001")
    print("Press Ctrl+C to stop the test at any time\n")
    
    try:
        # Test basic rate limiting
        test_rate_limiting()
        
        # Test rate limit recovery
        test_rate_limit_recovery()
        
        # Test health endpoint rate limiting
        test_health_endpoint_rate_limit()
        
        print("\n" + "=" * 60)
        print("Rate limiting tests completed!")
        
    except KeyboardInterrupt:
        print("\n\nTest interrupted by user.")
    except Exception as e:
        print(f"\nUnexpected error: {e}")
