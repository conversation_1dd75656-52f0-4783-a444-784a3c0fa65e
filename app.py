#!/usr/bin/env python3
"""
Flask HTTP server that accepts GET and POST requests and returns a hash of the submitted data.
"""

from flask import Flask, request, jsonify
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
import hashlib
import json
import time
from functools import lru_cache

app = Flask(__name__)

# Initialize the rate limiter
limiter = Limiter(
    app=app,
    key_func=get_remote_address,  # Rate limit based on IP address
    default_limits=["2 per 5 seconds"]  # Default rate limit: 2 requests per 5 seconds
)

# In-memory cache for storing computed hashes
# Structure: {data_key: {"hash": hash_value, "timestamp": creation_time}}
hash_cache = {}
CACHE_TTL = 300  # Cache time-to-live in seconds (5 minutes)

def clean_expired_cache():
    """Remove expired entries from the cache."""
    current_time = time.time()
    expired_keys = [
        key for key, value in hash_cache.items()
        if current_time - value["timestamp"] > CACHE_TTL
    ]
    for key in expired_keys:
        del hash_cache[key]

def get_cache_key(data):
    """Generate a consistent cache key from the data."""
    if isinstance(data, dict):
        # Convert dict to JSON string for consistent key generation
        return json.dumps(data, sort_keys=True)
    elif isinstance(data, str):
        return data
    else:
        return str(data)

def generate_hash_with_cache_info(data):
    """Generate SHA-256 hash of the provided data with caching and return cache info."""
    # Clean expired cache entries periodically
    clean_expired_cache()

    # Generate cache key
    cache_key = get_cache_key(data)

    # Check if hash is already cached
    if cache_key in hash_cache:
        cached_entry = hash_cache[cache_key]
        # Check if cache entry is still valid
        if time.time() - cached_entry["timestamp"] <= CACHE_TTL:
            return cached_entry["hash"], True  # Hash, is_cached
        else:
            # Remove expired entry
            del hash_cache[cache_key]

    # Generate new hash
    if isinstance(data, dict):
        # Convert dict to JSON string for consistent hashing
        data_str = json.dumps(data, sort_keys=True)
    elif isinstance(data, str):
        data_str = data
    else:
        data_str = str(data)

    hash_value = hashlib.sha256(data_str.encode('utf-8')).hexdigest()

    # Store in cache
    hash_cache[cache_key] = {
        "hash": hash_value,
        "timestamp": time.time()
    }

    return hash_value, False  # Hash, is_cached

@app.route('/', methods=['GET', 'POST'])
@limiter.limit("2 per 5 seconds")
def hash_data():
    """Handle both GET and POST requests and return hash of the data."""
    
    if request.method == 'GET':
        # For GET requests, hash the query parameters
        data = dict(request.args)
        if not data:
            data = "No query parameters provided"

        hash_value, is_cached = generate_hash_with_cache_info(data)

        return jsonify({
            'method': 'GET',
            'data': data,
            'hash': hash_value,
            'hash_algorithm': 'SHA-256',
            'cached': is_cached,
            'cache_info': 'Result retrieved from cache' if is_cached else 'Result computed and cached'
        })
    
    elif request.method == 'POST':
        # For POST requests, try to get JSON data first, then form data, then raw data
        if request.is_json:
            data = request.get_json()
        elif request.form:
            data = dict(request.form)
        else:
            data = request.get_data(as_text=True)
            if not data:
                data = "No data provided"

        hash_value, is_cached = generate_hash_with_cache_info(data)

        return jsonify({
            'method': 'POST',
            'data': data,
            'hash': hash_value,
            'hash_algorithm': 'SHA-256',
            'cached': is_cached,
            'cache_info': 'Result retrieved from cache' if is_cached else 'Result computed and cached'
        })

@app.route('/health', methods=['GET'])
@limiter.limit("10 per 5 seconds")  # More lenient for health checks
def health_check():
    """Simple health check endpoint."""
    return jsonify({
        'status': 'healthy',
        'message': 'Flask HTTP server is running'
    })

@app.route('/cache/stats', methods=['GET'])
@limiter.limit("5 per 5 seconds")
def cache_stats():
    """Get cache statistics."""
    # Clean expired entries before reporting stats
    clean_expired_cache()

    current_time = time.time()
    cache_entries = []

    for key, value in hash_cache.items():
        age_seconds = current_time - value["timestamp"]
        cache_entries.append({
            "key_preview": key[:50] + "..." if len(key) > 50 else key,
            "hash_preview": value["hash"][:16] + "...",
            "age_seconds": round(age_seconds, 2),
            "expires_in_seconds": round(CACHE_TTL - age_seconds, 2)
        })

    return jsonify({
        'cache_stats': {
            'total_entries': len(hash_cache),
            'cache_ttl_seconds': CACHE_TTL,
            'entries': cache_entries
        }
    })

@app.errorhandler(429)
def ratelimit_handler(e):
    """Handle rate limit exceeded errors."""
    return jsonify({
        'error': 'Rate limit exceeded',
        'message': 'Too many requests. Please wait before making another request.',
        'retry_after': str(e.retry_after) + ' seconds' if hasattr(e, 'retry_after') else 'a few seconds'
    }), 429



if __name__ == '__main__':
    print("Starting Flask HTTP server...")
    # print("Server will be available at: http://localhost:5000")
    # print("Health check endpoint: http://localhost:5000/health")

    app.run(host='0.0.0.0', port=5001, debug=False)
