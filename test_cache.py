#!/usr/bin/env python3
"""
Test script to demonstrate the caching functionality of the Flask server.
"""

import requests
import json
import time

BASE_URL = "http://localhost:5001"

def test_cache_functionality():
    """Test that identical requests are cached."""
    print("Testing Cache Functionality")
    print("=" * 60)
    
    # Test data that we'll send multiple times
    test_data = {
        "username": "cache_test_user",
        "message": "This should be cached",
        "timestamp": "2024-01-01T00:00:00Z"  # Fixed timestamp for consistent caching
    }
    
    print("1. First request (should compute hash and cache it):")
    try:
        start_time = time.time()
        response1 = requests.post(BASE_URL, json=test_data)
        end_time = time.time()
        
        if response1.status_code == 200:
            result1 = response1.json()
            print(f"   Status: {response1.status_code}")
            print(f"   Hash: {result1.get('hash', 'N/A')}")
            print(f"   Cached: {result1.get('cached', 'N/A')}")
            print(f"   Cache Info: {result1.get('cache_info', 'N/A')}")
            print(f"   Response Time: {round((end_time - start_time) * 1000, 2)}ms")
        else:
            print(f"   Error: Status {response1.status_code}")
            return
    except Exception as e:
        print(f"   Error: {e}")
        return
    
    print("\n2. Second request with identical data (should use cache):")
    try:
        start_time = time.time()
        response2 = requests.post(BASE_URL, json=test_data)
        end_time = time.time()
        
        if response2.status_code == 200:
            result2 = response2.json()
            print(f"   Status: {response2.status_code}")
            print(f"   Hash: {result2.get('hash', 'N/A')}")
            print(f"   Cached: {result2.get('cached', 'N/A')}")
            print(f"   Cache Info: {result2.get('cache_info', 'N/A')}")
            print(f"   Response Time: {round((end_time - start_time) * 1000, 2)}ms")
            
            # Verify hashes are identical
            if result1.get('hash') == result2.get('hash'):
                print("   ✓ Hashes match - cache working correctly!")
            else:
                print("   ✗ Hashes don't match - cache issue!")
        else:
            print(f"   Error: Status {response2.status_code}")
    except Exception as e:
        print(f"   Error: {e}")

def test_different_data_not_cached():
    """Test that different data is not cached together."""
    print("\n\nTesting Different Data (should not use cache)")
    print("=" * 60)
    
    # Two different sets of data
    data1 = {"message": "First message", "id": 1}
    data2 = {"message": "Second message", "id": 2}
    
    print("1. Request with first data set:")
    try:
        response1 = requests.post(BASE_URL, json=data1)
        if response1.status_code == 200:
            result1 = response1.json()
            print(f"   Hash: {result1.get('hash', 'N/A')[:16]}...")
            print(f"   Cached: {result1.get('cached', 'N/A')}")
        else:
            print(f"   Error: Status {response1.status_code}")
    except Exception as e:
        print(f"   Error: {e}")
    
    print("\n2. Request with second data set:")
    try:
        response2 = requests.post(BASE_URL, json=data2)
        if response2.status_code == 200:
            result2 = response2.json()
            print(f"   Hash: {result2.get('hash', 'N/A')[:16]}...")
            print(f"   Cached: {result2.get('cached', 'N/A')}")
            
            # Verify hashes are different
            if result1.get('hash') != result2.get('hash'):
                print("   ✓ Different hashes for different data - correct!")
            else:
                print("   ✗ Same hashes for different data - issue!")
        else:
            print(f"   Error: Status {response2.status_code}")
    except Exception as e:
        print(f"   Error: {e}")

def test_get_request_caching():
    """Test caching with GET requests."""
    print("\n\nTesting GET Request Caching")
    print("=" * 60)
    
    # Same query parameters
    params = {"name": "John", "age": "30", "city": "NYC"}
    
    print("1. First GET request:")
    try:
        response1 = requests.get(BASE_URL, params=params)
        if response1.status_code == 200:
            result1 = response1.json()
            print(f"   Hash: {result1.get('hash', 'N/A')[:16]}...")
            print(f"   Cached: {result1.get('cached', 'N/A')}")
        else:
            print(f"   Error: Status {response1.status_code}")
    except Exception as e:
        print(f"   Error: {e}")
    
    print("\n2. Second GET request with same parameters:")
    try:
        response2 = requests.get(BASE_URL, params=params)
        if response2.status_code == 200:
            result2 = response2.json()
            print(f"   Hash: {result2.get('hash', 'N/A')[:16]}...")
            print(f"   Cached: {result2.get('cached', 'N/A')}")
            
            if result1.get('hash') == result2.get('hash') and result2.get('cached'):
                print("   ✓ GET request caching working!")
            else:
                print("   ✗ GET request caching issue!")
        else:
            print(f"   Error: Status {response2.status_code}")
    except Exception as e:
        print(f"   Error: {e}")

def test_cache_stats():
    """Test the cache statistics endpoint."""
    print("\n\nTesting Cache Statistics")
    print("=" * 60)
    
    try:
        response = requests.get(f"{BASE_URL}/cache/stats")
        if response.status_code == 200:
            stats = response.json()
            cache_stats = stats.get('cache_stats', {})
            
            print(f"Total cache entries: {cache_stats.get('total_entries', 0)}")
            print(f"Cache TTL: {cache_stats.get('cache_ttl_seconds', 0)} seconds")
            
            entries = cache_stats.get('entries', [])
            if entries:
                print("\nCache entries:")
                for i, entry in enumerate(entries[:5]):  # Show first 5 entries
                    print(f"  {i+1}. Key: {entry.get('key_preview', 'N/A')}")
                    print(f"     Hash: {entry.get('hash_preview', 'N/A')}")
                    print(f"     Age: {entry.get('age_seconds', 0)}s")
                    print(f"     Expires in: {entry.get('expires_in_seconds', 0)}s")
                    print()
                
                if len(entries) > 5:
                    print(f"  ... and {len(entries) - 5} more entries")
            else:
                print("No cache entries found.")
        else:
            print(f"Error getting cache stats: Status {response.status_code}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == '__main__':
    print("Flask Cache Testing Script")
    print("Make sure the Flask server is running on port 5001")
    print("Press Ctrl+C to stop the test at any time\n")
    
    try:
        # Run all cache tests
        test_cache_functionality()
        test_different_data_not_cached()
        test_get_request_caching()
        test_cache_stats()
        
        print("\n" + "=" * 60)
        print("Cache testing completed!")
        print("Check the cache stats to see all cached entries.")
        
    except KeyboardInterrupt:
        print("\n\nTest interrupted by user.")
    except Exception as e:
        print(f"\nUnexpected error: {e}")
